const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');


const SCRAPER_NAME = 'BARDI';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile');

const LOGIN_URL = 'https://www.bardiauto.ro/webshop/';
const DEFAULT_TEST_PAGE = 'https://www.bardiauto.ro/webshop/';

const LOGGED_IN_SELECTOR = 'li.pm_customers'; // Presence means logged in
const LOGIN_LINK_SELECTOR = 'a#pm_login_link[data-toggle="dh_hb_menu"]';
const LOGIN_FORM_SELECTOR = 'form#login_form_on_hb';
const LOGIN_EMAIL_SELECTOR = 'input[name="email"]';
const LOGIN_PASSWORD_SELECTOR = 'input[name="jelszo"]';
const LOGIN_SUBMIT_SELECTOR = 'button.btn-dh-login';

// const RESULTS_SECTION_SELECTOR = 'section.grow.w-full';
// const RESULT_ITEMS_SELECTOR = 'ul.divide-y > li';

async function runBardiScraper(productCode) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  let browser;
  try {
    const { browser: launchedBrowser, page } = await launchBrowser({ userDataDir: PROFILE_PATH });
    browser = launchedBrowser;

    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, COOKIES_PATH);
    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await sleep(randomDelay(500, 1000));

    try {
      await page.waitForSelector(LOGGED_IN_SELECTOR, { timeout: 1000 });
      console.log('✅ Already logged in. Continuing...');
    } catch {
      console.log('🔐 Not logged in or session expired. Performing login...');

      const isOnLoginPage = await page.$(LOGIN_LINK_SELECTOR) !== null;

      if (!isOnLoginPage) {
        console.log(`Navigating to login page: ${LOGIN_URL}`);
        await page.goto(LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
      } else {
        console.log('🔄 Already on the login page.');
      }

      console.log('🖱️ Clicking "Autentificare" link...');
      await page.click(LOGIN_LINK_SELECTOR);

      console.log('⌛ Waiting for login form to appear...');
      await page.waitForSelector(LOGIN_FORM_SELECTOR, { timeout: 5000 });

      console.log('✏️ Typing username and password...');
      await page.type(LOGIN_EMAIL_SELECTOR, process.env.BARDI_USER, { delay: randomDelay(50, 100) });
      await sleep(randomDelay(300, 600));
      await page.type(LOGIN_PASSWORD_SELECTOR, process.env.BARDI_PASSWORD, { delay: randomDelay(50, 100) });
      await sleep(randomDelay(300, 600));

      console.log('🖱️ Submitting login form...');
      await page.click(LOGIN_SUBMIT_SELECTOR);

      console.log('⌛ Waiting for navigation after login...');
      await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });

      await saveCookiesToFile(page, COOKIES_PATH);
      saveSessionUrlToFile(page.url(), SESSION_URL_PATH);
    }

    const SEARCH_INPUT_SELECTOR = 'input#general_search_input';
    const GROUP_HEAD_LINK_SELECTOR = 'div.group-head h4 a';
    const NOT_FOUND_SELECTOR = 'div.text-center.not-found-message';

    // Use the exact product code as provided
    console.log(`🔎 Typing exact product code into search bar: ${productCode}`);

    await page.waitForSelector(SEARCH_INPUT_SELECTOR, { timeout: 5000 });
    await page.click(SEARCH_INPUT_SELECTOR);

    // Clear input
    await page.evaluate((selector) => {
      const input = document.querySelector(selector);
      if (input) input.value = '';
    }, SEARCH_INPUT_SELECTOR);
    await sleep(randomDelay(200, 300));

    // Type the exact product code as provided
    await page.type(SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay(50, 100) });
    await sleep(randomDelay(1000, 1500)); // Give time for suggestions or no-result message to appear
    await sleep(randomDelay(800, 1200));
    console.log('⌛ Checking if there are search results...');

    // Wait either for a suggestion OR a "no results" message
    const result = await Promise.race([
      page.waitForSelector(GROUP_HEAD_LINK_SELECTOR, { timeout: 5000 }).then(() => 'found'),
      page.waitForSelector(NOT_FOUND_SELECTOR, { timeout: 5000 }).then(() => 'not_found')
    ]);

    if (result === 'not_found') {
      console.log(`⚠️ No results found for ${productCode}. Exiting early.`);
      await browser.close();
      return []; // Return empty result
    }

    // Otherwise, continue and click the found link
    console.log('🖱️ Clicking on the autocomplete suggestion link...');
    await page.evaluate((productCode) => {
      const links = Array.from(document.querySelectorAll('div.group-head h4 a'));

      // Log all available links for debugging
      console.log('Available autocomplete links:');
      links.forEach(link => console.log(`- ${link.innerText}`));

      // Try to find a match using the exact product code (case-insensitive)
      let match = links.find(link =>
        link.innerText.toLowerCase().includes(productCode.toLowerCase())
      );

      if (match) {
        console.log(`Found and clicking on link: ${match.innerText}`);
        match.click();
      } else {
        console.log('No matching link found in autocomplete suggestions');
        // If no exact match, click the first link as a fallback
        if (links.length > 0) {
          console.log(`Clicking first available link as fallback: ${links[0].innerText}`);
          links[0].click();
        }
      }
    }, productCode);

    console.log('⌛ Waiting for search results page to load...');
    await page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 15000 });
    await sleep(randomDelay(800, 1200));

    // Define selectors for both possible outcomes
    const RESULTS_TABLE_SELECTOR = 'table.search-result-list';
    const RELOGIN_PASSWORD_SELECTOR = 'input#auth_pass';

    console.log('⌛ Checking if we need to re-login or if results are displayed...');

    // Check if the re-login form appears
    try {
      // Use a custom selector function to find the re-login form
      const reloginForm = await page.evaluate(() => {
        const heading = Array.from(document.querySelectorAll('h1.text-center.m-b-15')).find(
          el => el.textContent.includes('Confirma logarea')
        );
        return heading !== undefined;
      });

      if (reloginForm) {
        console.log('🔐 Security re-login form detected. Filling in password...');

        // Wait for the password field to be visible
        await page.waitForSelector(RELOGIN_PASSWORD_SELECTOR, { timeout: 5000 });

        // Type the password
        await page.type(RELOGIN_PASSWORD_SELECTOR, process.env.BARDI_PASSWORD, { delay: randomDelay(50, 100) });
        await sleep(randomDelay(300, 600));

        // Find and click the submit button
        const submitButton = await page.evaluate(() => {
          const button = Array.from(document.querySelectorAll('button.btn.btn-primary')).find(
            el => el.textContent.includes('Confirmare')
          );
          if (button) {
            button.click();
            return true;
          }
          return false;
        });

        if (submitButton) {
          console.log('🖱️ Clicked re-login submit button. Waiting for navigation...');
          await page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 15000 });
          await sleep(randomDelay(800, 1200));
          console.log('✅ Re-login successful. Continuing with search results...');
        } else {
          console.log('⚠️ Could not find the re-login submit button.');
        }
      } else {
        console.log('✅ No re-login required. Search results displayed directly.');
      }
    } catch (error) {
      console.log('⚠️ Error checking for re-login form:', error.message);
      console.log('Continuing with normal flow...');
    }

    console.log('⌛ Waiting for results table to appear...');
    await page.waitForSelector(RESULTS_TABLE_SELECTOR, { timeout: 10000 });

    // ✅ Add this log right after page loads
    console.log('📄 Results page loaded successfully!');

    // page.on('console', msg => {
    //   for (let i = 0; i < msg.args().length; ++i)
    //     msg.args()[i].jsonValue().then(v => console.log(`🖥️ [browser log]: ${v}`));
    // });


    await sleep(randomDelay(800, 1200));

    const scrapedData = await page.evaluate((resultsTableSel, scraperName) => {
      const tbodies = Array.from(document.querySelectorAll(`${resultsTableSel} tbody.on-by-vin-modal`));
      const products = [];

      for (const tbody of tbodies) {
        const firstRow = tbody.querySelector('tr.search-result-list-content');
        if (!firstRow) {
          continue;
        }

        const tdElements = firstRow.querySelectorAll('td');

        if (tdElements.length >= 4) {
          const brandAndNameTd = tdElements[0];
          const deliveryOptionsTd = tdElements[1];
          const mainCodeTd = tdElements[2];
          const priceTd = tdElements[3];

          // Extract brand
          const brandSpan = brandAndNameTd.querySelector('span.text-bold.text-secondary');
          const brand = brandSpan ? brandSpan.textContent.trim() : 'N/A';

          // Extract name
          let name = 'N/A';
          if (brandSpan && brandSpan.nextSibling) {
            name = brandSpan.nextSibling.textContent.trim();
            if (name.startsWith('-')) {
              name = name.slice(1).trim();
            }
          }

          // Extract mainCode (this will be normalized later)
          const mainCode = mainCodeTd ? mainCodeTd.textContent.trim() : null;

          // Extract price
          const priceDiv = priceTd.querySelector('div.price-text-on-table-cell');
          const price = priceDiv ? priceDiv.innerText.trim() : null;

          // Convert price to double (e.g., "1.234,56 lei" -> 1234.56)
          let formattedPrice = null;
          if (price) {
            // If the price is just "-", keep it as null
            if (price === "-") {
              formattedPrice = null;
            } else {
              // Extract numeric part and handle European number format
              const match = price.match(/([\d.,]+)/);
              if (match) {
                // First, remove all dots (thousands separators)
                // Then, replace comma with dot (decimal separator)
                const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
                const numeric = parseFloat(cleanedNumber);
                if (!isNaN(numeric)) {
                  formattedPrice = numeric;
                } else {
                  // Keep original if parsing fails
                  formattedPrice = price;
                }
              } else {
                // Keep original if no numeric part found
                formattedPrice = price;
              }
            }
          }

          let deliveryOptions = 'N/A';
          if (deliveryOptionsTd) {
            const icons = deliveryOptionsTd.querySelectorAll('span i');
            const total = icons.length;
            const available = Array.from(icons).filter(icon => icon.classList.contains('ba-check-square')).length;
            if (total === 0 ) {
              continue; // Skip this product
            }
            deliveryOptions = `${available}/${total}`;
          }

          // Extract exchange value
          const exchangeTd = tdElements[tdElements.length - 1]; // Last td
          let exchangeValue = null;
          if (exchangeTd) {
            const span = exchangeTd.querySelector('div.product-box-price-deposit span');
            if (span) {
              const rawText = span.textContent.trim();

              // If the exchange value is just "-", set it to null
              if (rawText === "-") {
                exchangeValue = null;
              } else {
                // Extract numeric part (e.g., "+ 595,00 lei" -> "595,00")
                const match = rawText.match(/([\d.,]+)/);
                if (match) {
                  // Handle European number format (595,00)
                  // First, remove all dots (thousands separators)
                  // Then, replace comma with dot (decimal separator)
                  const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
                  const numeric = parseFloat(cleanedNumber);
                  if (!isNaN(numeric)) {
                    exchangeValue = numeric;
                  } else {
                    // Keep original if parsing fails
                    exchangeValue = rawText;
                  }
                } else {
                  // Keep original if no numeric part found
                  exchangeValue = rawText;
                }
              }
            }
          }

          // Get the second row with detailed information
          const secondRow = tbody.querySelector('tr.product-box');
          let detailedAvailability = null;
          let deliveryTime = null;

          if (secondRow) {
            // Extract detailed availability information from the store info boxes
            const storeInfoContainer = secondRow.querySelector('.ba-box-col-xs-1.p-l-5.p-r-5.text-right');
            if (storeInfoContainer) {
              const storeInfoBoxes = storeInfoContainer.querySelectorAll('.store-info-box.ba-box-row');
              if (storeInfoBoxes && storeInfoBoxes.length > 0) {
                const availabilityDetails = [];

                storeInfoBoxes.forEach(box => {
                  let plantName = box.querySelector('.store-info-box-branch')?.textContent.trim();
                  const isAvailable = box.querySelector('.ba-check-square') !== null;
                  const quantitySpan = box.querySelector('.store-info-box-branch span span');
                  let quantity = null;

                  if (quantitySpan) {
                    const quantityMatch = quantitySpan.textContent.match(/\((\d+)\s+buc\)/);
                    if (quantityMatch) {
                      quantity = quantityMatch[1];
                    }
                  }

                  if (plantName) {
                    // Clean up plant name by removing newlines and extra whitespace
                    plantName = plantName.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();

                    let availabilityInfo = `${plantName}:${isAvailable ? 'true' : 'false'}`;
                    if (quantity) {
                      //availabilityInfo += `:${quantity}`;
                    }
                    availabilityDetails.push(availabilityInfo);
                  }
                });

                if (availabilityDetails.length > 0) {
                  detailedAvailability = availabilityDetails.join('/');
                }
              }
            }

            // Extract delivery time information
            let deliveryContainer = secondRow.querySelector('.ba-box-col-xs-1.p-0.p-r-5.b-r-1.b-r-dashed');

            if (deliveryContainer) {
              // Method 1: Try to find the standard delivery time element (paragraph)
              const deliveryTimeElement = deliveryContainer.querySelector('p.m-t-5.bold.text-bold');
              if (deliveryTimeElement) {
                deliveryTime = deliveryTimeElement.textContent.trim();
              } else {
                // Method 2: Try to find the immediate delivery format with truck icon
                const truckDeliveryTitle = deliveryContainer.querySelector('.deliver-title.deliver-truck');
                if (truckDeliveryTitle) {
                  // Get the next div after the truck delivery title which contains the delivery time
                  const truckDeliveryInfo = truckDeliveryTitle.parentElement.querySelector('div:not(.deliverytablerow)');
                  if (truckDeliveryInfo) {
                    // Extract the complete text including both the bold part and the additional context
                    deliveryTime = truckDeliveryInfo.textContent.trim()
                      .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
                      .replace(/\n/g, ' '); // Replace newlines with spaces
                  }
                } else {
                  // Method 3: Fallback - try to extract any delivery information from the container
                  // Look for any bold text that might contain delivery information
                  const boldElements = deliveryContainer.querySelectorAll('b');
                  if (boldElements.length > 0) {
                    // Try to find a bold element that contains a day of the week or time
                    const dayTimeRegex = /(luni|marti|miercuri|joi|vineri|sambata|duminica|\d+:\d+)/i;
                    for (const element of boldElements) {
                      if (dayTimeRegex.test(element.textContent)) {
                        // Get the parent element's text which contains the full delivery info
                        // including both the bold part and the additional context
                        const parentText = element.parentElement.textContent.trim()
                          .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
                          .replace(/\n/g, ' '); // Replace newlines with spaces

                        if (parentText) {
                          deliveryTime = parentText;
                          break;
                        }
                      }
                    }
                  }

                  // If still no delivery time found, try to get any text that mentions delivery terms
                  if (!deliveryTime) {
                    const deliveryTermsText = deliveryContainer.textContent.trim()
                      .replace(/Cand pot prelua\?/i, '')
                      .replace(/Apăsați aici pentru termeni de livrare!/i, '')
                      .trim();

                    if (deliveryTermsText) {
                      // Try to extract a concise delivery information
                      const lines = deliveryTermsText.split('\n')
                        .map(line => line.trim())
                        .filter(line => line && line.length > 5); // Filter out empty or very short lines

                      if (lines.length > 0) {
                        // Use the most relevant line (usually contains time or days)
                        for (const line of lines) {
                          if (line.match(/(\d+[:-]\d+|zi|zile|ore|livrare)/i)) {
                            deliveryTime = line;
                            break;
                          }
                        }

                        // If no specific line found, use the first non-empty line
                        if (!deliveryTime && lines.length > 0) {
                          deliveryTime = lines[0];
                        }
                      }
                    }
                  }
                }
              }
            }
          }

          // Save product only if mainCode is valid and price is not "-"
          if (mainCode && price !== '-') {
            products.push({
              name,
              brand,
              productCode: mainCode,
              internalCode: null,
              delivery: deliveryTime,
              retailPrice: formattedPrice, // Now contains the numeric value or original string if parsing failed
              exchangeValue, // Now contains the numeric value or original string if parsing failed
              availability: detailedAvailability || deliveryOptions, // Use detailed availability if available
              returnable: null,
              provider: scraperName
            });
          }
        } else {
          console.log('⚠️ Not enough td elements to extract data.');
        }
      }

      return products;
    }, 'table.search-result-list', SCRAPER_NAME);


    console.log(`📊 Scraped ${scrapedData.length} products.`);


    await browser.close();
    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully.`);
    return scrapedData;

  } catch (err) {
    console.error(`❌ Error in ${SCRAPER_NAME}:`, err);
    if (browser) await browser.close();
    return null;
  }
}

module.exports = {
  runBardiScraper,
};

