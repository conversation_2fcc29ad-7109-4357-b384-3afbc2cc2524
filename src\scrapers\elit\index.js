const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');

const SCRAPER_NAME = 'ELIT';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile');

const LOGIN_URL = 'https://www.elit.ro';
const DEFAULT_TEST_PAGE = 'https://www.elit.ro';

const LOGGED_IN_SELECTOR = '.nav-top-user-label';
const OPEN_AUTH_FORM_SELECTOR = '#AKLoginDialog > a'; // Clicking "Accesare"
const USERNAME_SELECTOR = '#UserName'; // Username input field
const PASSWORD_SELECTOR = '#Password'; // Password input field
const LOGIN_BUTTON_SELECTOR = '#submitButton'; // "Accesează" login button

const SEARCH_INPUT_SELECTOR = '#SearchFocus';
const SEARCH_BUTTON_SELECTOR = 'button.btn.btn-primary.hoverBTN';
const SHOW_ALL_RESULTS_SELECTOR = 'a.btn-show-more';
const PRICE_SELECTOR = '.price_final > span';
const RESULTS_TABLE_SELECTOR = '.art_wrapper';
const PRICE_REQUEST_BUTTON_SELECTOR = 'button.btn-lightx.bold';

async function runElitScraper(productCode) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  let browser;
  try {
    const { browser: launchedBrowser, page } = await launchBrowser({ userDataDir: PROFILE_PATH });
    browser = launchedBrowser;

    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, COOKIES_PATH);
    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await sleep(randomDelay(500, 1000));

    const COOKIE_CONSENT_SELECTOR = '#onetrust-accept-btn-handler';
    const cookieAcceptButton = await page.$(COOKIE_CONSENT_SELECTOR);
    if (cookieAcceptButton) {
      console.log('🍪 Accepting cookie consent...');
      await cookieAcceptButton.click();
      await sleep(randomDelay(300, 600)); // Wait a bit after accepting
    }

    try {
      console.log('🔑 Already logged in...');
      await page.waitForSelector(LOGGED_IN_SELECTOR, { timeout: 3000 });
    } catch {
      console.log('🔐 Not logged in or session expired. Performing login...');

      const isOnLoginPage = await page.$(OPEN_AUTH_FORM_SELECTOR) !== null;

      if (!isOnLoginPage) {
        console.log(`Navigating to login page: ${LOGIN_URL}`);
        await page.goto(LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: 15000 });
      } else {
        console.log('🔄 Already on the login page.');
      }

      await page.waitForSelector(OPEN_AUTH_FORM_SELECTOR);
      await page.click(OPEN_AUTH_FORM_SELECTOR);
      await sleep(randomDelay(300, 600));
      await page.waitForSelector(USERNAME_SELECTOR);
      await page.type(USERNAME_SELECTOR, process.env.ELIT_USER, { delay: randomDelay() });
      await sleep(randomDelay(300, 600));
      await page.type(PASSWORD_SELECTOR, process.env.ELIT_PASSWORD, { delay: randomDelay() });
      await sleep(randomDelay(300, 600));
      await page.click(LOGIN_BUTTON_SELECTOR);
      await page.waitForNavigation({ waitUntil: 'networkidle2' });
      await saveCookiesToFile(page, COOKIES_PATH);
      saveSessionUrlToFile(page.url(), SESSION_URL_PATH);
    }

    await page.waitForSelector(SEARCH_INPUT_SELECTOR);
    await page.evaluate(sel => document.querySelector(sel).value = '', SEARCH_INPUT_SELECTOR);
    await page.type(SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay() });
    await sleep(randomDelay(300, 600));
    await page.click(SEARCH_BUTTON_SELECTOR);
    await sleep(randomDelay(500, 1000));
    // Wait for and click the "Show all results" button if it appears
    try {
      await page.waitForSelector(SHOW_ALL_RESULTS_SELECTOR, { timeout: 5000 });
      console.log('🔍 Clicking "Show all results" button...');
      await sleep(randomDelay(500, 1000));
      await page.click(SHOW_ALL_RESULTS_SELECTOR);
      await sleep(randomDelay(500, 1000));
    } catch (e) {
      console.warn('⚠️ "Show all results" button not found — continuing anyway.');
    }



    const paginationIsVisible = await page.evaluate(() => {
      const container = document.querySelector('div[data-ng-show="paging.Total > 1"]');
      return container && !container.classList.contains('ng-hide');
    });

    if (!paginationIsVisible) {
      console.log('🟢 Single page of results — scraping directly.');
      const singlePageResults = await scrapeCurrentPage(page);
      // console.log(`📊 Scraped ${singlePageResults.length} products.`);
      // console.log(JSON.stringify(singlePageResults, null, 2));
      //await browser.close();
      return singlePageResults;
    }

    const visitedPages = new Set();
    const allResults = [];

    while (true) {
      await sleep(randomDelay(500, 1000));
      await page.waitForSelector('ul.pagination', { visible: true, timeout: 10000 });

      const activePage = await page.evaluate(() => {
        const active = document.querySelector('ul.pagination li.active a');
        return active ? active.textContent.trim() : null;
      });

      if (!activePage || visitedPages.has(activePage)) break;
      visitedPages.add(activePage);

      console.log(`📄 Visited page: ${activePage}`);

      // Wait for delivery info (already included in your original)
      const maxWait = 25000;
      const interval = 400;
      const start = Date.now();
      let deliveryResolved = false;

      while (Date.now() - start < maxWait) {
        const result = await page.evaluate(() => {
          const rows = Array.from(document.querySelectorAll('tr.table-row-main'));
          if (rows.length === 0) return { total: 0, ready: 0 };
          const readyCount = rows.filter(row => {
            const spans = row.querySelectorAll('.TakeOffStorage span.ng-binding');
            return Array.from(spans).some(span => span.textContent.trim().length > 0);
          }).length;
          return { total: rows.length, ready: readyCount };
        });

        console.log(`📦 ${result.ready}/${result.total} rows have delivery info.`);
        if (result.total > 0 && result.ready === result.total) {
          deliveryResolved = true;
          break;
        }

        await new Promise(res => setTimeout(res, interval));
      }

      if (deliveryResolved) {
        console.log(`✅ All product rows on page ${activePage} have delivery info.`);
      } else {
        console.warn(`⚠️ Some product rows on page ${activePage} might be missing delivery info after timeout.`);
      }

      // 🧲 Scrape the current page and add to results
      const currentProducts = await scrapeCurrentPage(page);
      allResults.push(...currentProducts);

      // Handle pagination
      const allPageItems = await page.$$('ul.pagination li');
      const validPageLinks = allPageItems.slice(2, -2) // Skip first two and last two
        .map(async li => {
          const link = await li.$('a');
          if (!link) return null;
          const text = await page.evaluate(el => el.textContent.trim(), link);
          return { element: link, text };
        });

      const resolvedLinks = (await Promise.all(validPageLinks)).filter(Boolean);

      let foundNext = false;
      for (const { element, text } of resolvedLinks) {
        if (!visitedPages.has(text)) {
          await element.click();
          await page.waitForFunction(() => !document.querySelector('ul.pagination li.active a.loading'), { timeout: 10000 });
          await sleep(randomDelay(1000, 1500));
          foundNext = true;
          break;
        }
      }

      if (!foundNext) break;
    }


    const results = allResults;

    // console.log(`📊 Scraped ${results.length} products.`);
    // console.log(JSON.stringify(results, null, 2)); // Output results
    //await browser.close();
    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully. Total products: ${allResults.length}`);
    return results;

  } catch (err) {
    console.error(`❌ Error in ${SCRAPER_NAME}:`, err);
    if (browser) await browser.close();
    return null;
  }
}
async function scrapeCurrentPage(page) {
  console.log('🧲 Scraping ELIT product data from current page...');
  await sleep(randomDelay(500, 1000));

  const products = await page.evaluate(() => {
    const data = [];
    const rows = document.querySelectorAll('tr.table-row-main');

    rows.forEach(row => {
      const name = row.querySelector('.ProductName')?.textContent.trim() || null;

      const refCodeEl = row.querySelector(
        '.productCodeHighlighted[data-ng-show*="product.ReferenceCode"]'
      );
      const productCode = refCodeEl?.textContent.trim() || null;

      const brand = row.querySelector('.BrandProduct')?.textContent.trim() || null;

      const deliverySpan = row.querySelector(
        'td.TakeOffStorage span[data-ng-bind-html="product.Disponibility.Text"]'
      );
      let delivery = deliverySpan?.textContent.trim() || null;

      // Replace "Întrebare la tel." with null
      if (delivery === "Întrebare la tel.") {
        delivery = null;
      }

      const priceText = row.querySelector('span[data-ng-bind="product.PriceVat"]')?.textContent.trim().replace(/[^\d,]/g, '').replace(',', '.');
      const price = priceText ? parseFloat(priceText) : null;

      // Extract quantity information
      const qtySpan = row.querySelector(
        'td.TakeOffStorage span[data-ng-bind="product.Disponibility.AmountText"]'
      );
      const quantityText = qtySpan?.textContent.trim() || null;

      // Extract order deadline information
      const orderDeadlineSpan = row.querySelector(
        'td.TakeOffStorage span.stock-disponibility-validto span[data-ng-bind-html="product.Disponibility.ValidTo"]'
      );
      const orderDeadline = orderDeadlineSpan?.textContent.trim() || null;

      // Create a comprehensive availability string
      let availability = null;
      if (quantityText) {
        availability = quantityText;
        if (orderDeadline) {
          availability += ` (order by: ${orderDeadline})`;
        }
      }

      const nonReturnable = Array.from(row.querySelectorAll('img')).some(img => {
        const src = img.getAttribute('src') || '';
        const isNonReturnableIcon = src.includes('/images/NonReversible.png');
        const isHidden = img.classList.contains('ng-hide');
        return isNonReturnableIcon && !isHidden;
      });

      const returnable = !nonReturnable;

      const exchangeSpan = row.querySelector('span.icon-recycle_elit');
      let exchangeValue = null;
      if (exchangeSpan && exchangeSpan.hasAttribute('data-original-title')) {
        const tooltip = exchangeSpan.getAttribute('data-original-title');
        const match = tooltip.match(/([\d.,]+)\s*LEI/);
        if (match) {
          exchangeValue = parseFloat(match[1].replace(',', '.').replace(/\s/g, ''));
        }
}

      if (name && productCode) {
        data.push({
          name,
          brand,
          productCode,
          internalCode: null,
          delivery,
          //minimumOrderQty,
          retailPrice: price,
          exchangeValue,
          availability: availability, // Contains quantity and order deadline information
          returnable,
          provider: 'ELIT',
        });
      }
    });

    return data;
  });
 
  console.log(`📦 Extracted ${products.length} products from page.`);
  return products;
}
module.exports = {
  runElitScraper,
};

