const config = require('./config');

/**
 * Waits for all products to have price information
 * @param {Object} page - Puppeteer page object
 * @returns {Promise<boolean>} - True if all products have price info
 */
async function waitForProductPrices(page) {
  console.log('⏳ Waiting for all products to have price or request button...');
  try {
    await page.waitForFunction(() => {
      const productCards = document.querySelectorAll('.art_wrapper .article-card');
      return Array.from(productCards).every(card => {
        const hasPrice = card.querySelector('.price_final span');
        const hasButton = card.querySelector('button.btn-lightx.bold');
        return hasPrice || hasButton;
      });
    }, { timeout: config.PRICE_INFO_TIMEOUT });
    console.log('✅ All products have price info. Scraping...');
    return true;
  } catch {
    console.warn('⚠️ Not all products had price info after timeout. Continuing anyway...');
    return false;
  }
}

/**
 * Extracts product data from the current page
 * @param {Object} page - Puppeteer page object
 * @param {Array<string>} brandNames - List of brand names for parsing
 * @returns {Promise<Array<Object>>} - Array of extracted product objects
 */
async function extractProductsFromPage(page, brandNames) {
  await waitForProductPrices(page);

  return await page.evaluate((brandNames, selectors) => {
    // Helper function to parse title and extract brand and product code
    function parseTitleWithRegex(titleText, brandNames) {
      // Escape & sort brands by length (longest first for better matching)
      const escaped = brandNames
        .map(b => b.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
        .sort((a, b) => b.length - a.length);

      // Build regex pattern: ^(BrandA|Brand B|...)\s+(.+)$
      const re = new RegExp(`^(${escaped.join('|')})\\s+(.+)$`, 'i');
      const m = titleText.match(re);

      if (m) {
        return {
          brand: m[1],
          productCode: m[2].trim(),
        };
      }

      // Fallback if no brand match
      const [first, ...rest] = titleText.split(' ');
      return { brand: first, productCode: rest.join(' ') };
    }

    // Process all product cards on the page
    const items = [];
    const productCards = document.querySelectorAll(selectors.SEARCH.PRODUCT_CARDS);

    productCards.forEach(card => {
      // Extract title and parse brand/product code
      const titleText = card.querySelector(selectors.SEARCH.TITLE)?.textContent.trim() || '';
      const { brand, productCode } = parseTitleWithRegex(titleText, brandNames);

      // Extract product name/description
      const name = card.querySelector(selectors.SEARCH.DESCRIPTION)?.textContent.trim() || null;

      // Extract price information
      const priceText = card.querySelector(selectors.SEARCH.PRICE)?.textContent.trim();
      let price = null;

      if (priceText) {
        // Clean and parse the price, then apply the formula
        const numeric = parseFloat(priceText.replace(/[^\d,]/g, '').replace(',', '.'));
        if (!isNaN(numeric)) {
          // Convert to number using the same formula as before
          price = Math.round(numeric * 1.19 * 1.2) / 100;
        } else {
          // If parsing fails, keep the original text
          price = priceText;
        }
      } else {
        // If price is available via request, keep price as null
        // This is more consistent across all scrapers
        price = null;
      }

      // Extract delivery information
      const delivery = card.querySelector(selectors.SEARCH.DELIVERY)?.textContent.trim() || null;

      // Extract exchange value if available
      let exchangeValue = null;
      const piesaSchimbBlock = card.querySelector(selectors.SEARCH.EXCHANGE_VALUE_WRAPPER);
      if (piesaSchimbBlock && piesaSchimbBlock.innerText.includes('Piesa schimb')) {
        const spans = piesaSchimbBlock.querySelectorAll('span');
        if (spans.length > 1) {
          const rawValue = spans[1].textContent.trim();
          const parsed = parseFloat(rawValue.replace(/[^\d,]/g, '').replace(',', '.'));
          if (!isNaN(parsed)) {
            exchangeValue = parsed / 100;
          }
        }
      }

      // Only add products with a valid product code
      if (productCode) {
        // Determine if the product is returnable based on delivery information
        const isReturnable = delivery ?
          !(
            delivery.toLowerCase().includes('express') ||
            delivery === 'Verifica disponibilitatea' ||
            delivery.startsWith('Termen mediu')
          ) :
          true;

        items.push({
          name,
          brand: brand || null,
          productCode,
          internalCode: null,
          delivery: delivery || null,
          retailPrice: price,
          exchangeValue,
          availability: null,
          returnable: isReturnable,
          provider: 'AUTOTOTAL'
        });
      }
    });

    return items;
  }, brandNames, config.SELECTORS);
}

module.exports = {
  waitForProductPrices,
  extractProductsFromPage
};
