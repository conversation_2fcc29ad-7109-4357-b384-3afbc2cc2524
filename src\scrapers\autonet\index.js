const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') }); // Load .env from root

const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');

// --- Autonet Specific Configuration ---
const SCRAPER_NAME = 'AUTONET';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile'); // For userDataDir

const config = {
  // URLs and Selectors
  LOGIN_URL: 'https://www.webcat-solutions.com/ro/portal/loginNew.aspx',
  DEFAULT_TEST_PAGE: 'https://web4.carparts-cat.com/Default.aspx', // A page accessible after login
  LOGGED_IN_SELECTOR: '#tp_articlesearch_txt_art_direkt', // Element indicating logged-in state
  USERNAME_SELECTOR: '#txtUser',
  PASSWORD_SELECTOR: '#txtPassword',
  LOGIN_BUTTON_SELECTOR: '#btnLogin',
  COOKIE_MODAL_ACCEPT_SELECTOR: '#bs-gdpr-cookies-modal-decline-all-btn',
  SEARCH_INPUT_SELECTOR: '#tp_articlesearch_txt_art_direkt',
  SEARCH_BUTTON_SELECTOR: '#tp_articlesearch_imgBtn',
  PRICE_SELECTOR: 'td.tbl_al_erp_price_spalte_prVal span[pricefiltercat="0"]',
  RESULTS_TABLE_SELECTOR: '#main_artikel_panel_maintable',
  OEM_RESULTS_TABLE_SELECTOR: '#main_artikel_panel_oenr_maintable',

  // Timing Delays (in milliseconds)
  navigationDelay: { min: 500, max: 1000 },      // After page navigation (goto)
  afterCookieAcceptDelay: { min: 500, max: 1000 }, // After clicking cookie accept
  inputFieldDelay: { min: 300, max: 600 },       // Between filling username/password
  beforeLoginClickDelay: { min: 300, max: 600 }, // After filling password, before hover/click
  afterLoginHoverDelay: { min: 300, max: 600 },  // After hovering login button
  afterSearchInputDelay: { min: 300, max: 600 }, // After typing search code
  postPriceLoadDelay: 500,                     // Fixed delay after prices appear loaded

  // Timeouts (in milliseconds)
  navigationTimeout: 60000,        // 1 minute for normal networks
  loginCheckTimeout: 1000,         // 1 second
  cookieModalTimeout: 1000,        // 1 second
  loginConfirmationTimeout: 30000, // 30 seconds
  priceLoadTimeout: 60000,         // 30 seconds
};
// --- End Configuration ---


/**
 * Main function to run the Autonet scraper.
 * @param {string} productCode - The product code to search for.
 * @param {object} [networkThrottling=null] - Optional network throttling configuration.
 *   When null (default), no throttling is applied.
 *   When provided, should contain: {downloadSpeed, uploadSpeed, latency} in bytes/second and milliseconds.
 * @returns {Promise<Array|null>} Array of scraped products or null if scraping failed.
 */
async function runAutonetScraper(productCode, networkThrottling = null) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  let browser;
  try {
    // Launch browser with dedicated profile for this scraper and optional network throttling
    const { browser: launchedBrowser, page } = await launchBrowser({
      userDataDir: PROFILE_PATH,
      networkThrottling
    });
    browser = launchedBrowser;

    // Determine start page: saved session URL or default test page
    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || config.DEFAULT_TEST_PAGE;

    // Try loading cookies first
    await loadCookiesFromFile(page, COOKIES_PATH);

    // Network throttling is handled by the launchBrowser function

    console.log(`Navigating to test page: ${testPage}`);
    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: config.navigationTimeout });
    await sleep(randomDelay(config.navigationDelay.min, config.navigationDelay.max));

    // Check if we’re still logged in
    let stillLoggedIn = false;
    try {
        await page.waitForSelector(config.LOGGED_IN_SELECTOR, { timeout: config.loginCheckTimeout });
        stillLoggedIn = true;
    } catch (error) {
        console.log('Login check selector not found, assuming not logged in.');
        stillLoggedIn = false;
    }


    if (stillLoggedIn) {
      console.log('✅ Already logged in via session/cookies.');
      // Verify the session URL is still valid, update if redirected
      const currentUrl = page.url();
      if (currentUrl !== testPage) {
          console.log(`🔄 Session URL changed to: ${currentUrl}. Saving...`);
          saveSessionUrlToFile(currentUrl, SESSION_URL_PATH);
      }

    } else {
      console.log('🔐 Not logged in or session expired. Performing login...');

      // Check if we are already on the login page (e.g., due to redirect)
      const isOnLoginPage = await page.$(config.USERNAME_SELECTOR) !== null;

      if (!isOnLoginPage) {
        console.log(`Navigating to login page: ${config.LOGIN_URL}`);
        await page.goto(config.LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: config.navigationTimeout });
      } else {
        console.log('🔄 Already on the login page.');
      }

      // Accept cookie modal if present (might appear even if already on login page)
      try {
        await page.waitForSelector(config.COOKIE_MODAL_ACCEPT_SELECTOR, { timeout: config.cookieModalTimeout, visible: true });
        console.log('🍪 Accepting cookie prompt...');
        await page.click(config.COOKIE_MODAL_ACCEPT_SELECTOR);
        await sleep(randomDelay(config.afterCookieAcceptDelay.min, config.afterCookieAcceptDelay.max));
      } catch (err) {
        console.log('🍪 Cookie modal not found or timed out.');
      }

      // Login steps
      console.log('🔑 Entering credentials...');
      await page.waitForSelector(config.USERNAME_SELECTOR, { visible: true });
      await page.click(config.USERNAME_SELECTOR);
      // Clear the field before typing
      await page.evaluate((selector) => { document.querySelector(selector).value = ''; }, config.USERNAME_SELECTOR);
      // Note: Using default randomDelay() here as type delay is less critical, but could be configured
      await page.type(config.USERNAME_SELECTOR, process.env.AUTONET_USER || '', { delay: randomDelay() });

      await sleep(randomDelay(config.inputFieldDelay.min, config.inputFieldDelay.max));

      await page.waitForSelector(config.PASSWORD_SELECTOR, { visible: true });
      await page.click(config.PASSWORD_SELECTOR);
      // Clear the field before typing
      await page.evaluate((selector) => { document.querySelector(selector).value = ''; }, config.PASSWORD_SELECTOR);
      await page.type(config.PASSWORD_SELECTOR, process.env.AUTONET_PASSWORD || '', { delay: randomDelay() });

      await sleep(randomDelay(config.beforeLoginClickDelay.min, config.beforeLoginClickDelay.max));
      console.log('🖱️ Clicking login button...');
      await page.hover(config.LOGIN_BUTTON_SELECTOR);
      await sleep(randomDelay(config.afterLoginHoverDelay.min, config.afterLoginHoverDelay.max));
      await page.click(config.LOGIN_BUTTON_SELECTOR);

      // Wait for navigation/redirect after login, check for logged-in element
      console.log('⏳ Waiting for login confirmation...');
      try {
          await page.waitForSelector(config.LOGGED_IN_SELECTOR, { timeout: config.loginConfirmationTimeout });
          console.log('✅ Login successful!');

          // Save new cookies and session URL
          await saveCookiesToFile(page, COOKIES_PATH);
          const currentUrl = page.url();
          console.log(`🔗 Logged-in URL: ${currentUrl}`);
          saveSessionUrlToFile(currentUrl, SESSION_URL_PATH);

      } catch (loginError) {
          console.error('❌ Login failed. Could not find logged-in element after submitting credentials.', loginError);
          await browser.close();
          return null; // Indicate failure
      }
    }

    // --- Scraping Logic ---
    console.log(`🔍 Searching for product code: ${productCode}...`);

    await page.waitForSelector(config.SEARCH_INPUT_SELECTOR, { visible: true });
    await page.click(config.SEARCH_INPUT_SELECTOR);
    // Clear potential existing value
    await page.evaluate((selector) => { document.querySelector(selector).value = ''; }, config.SEARCH_INPUT_SELECTOR);
    await page.type(config.SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay() });
    await sleep(randomDelay(config.afterSearchInputDelay.min, config.afterSearchInputDelay.max));
    await page.click(config.SEARCH_BUTTON_SELECTOR);

    console.log('⏳ Waiting for search results and prices to load...');
    try {
        // Wait until at least one real price is visible
        await page.waitForFunction((priceSel) => {
            const spans = Array.from(document.querySelectorAll(priceSel));
            return spans.some(span => {
            const text = span.textContent.trim();
            return text && text !== '';
            });
        }, { timeout: config.priceLoadTimeout }, config.PRICE_SELECTOR);

        await sleep(config.postPriceLoadDelay); // Use fixed delay from config
        console.log('💰 Prices loaded, scraping data...');
    } catch (priceWaitError) {
        console.error('❌ Timed out waiting for prices to load.', priceWaitError);
        // Check if results table exists at all
        const resultsTableExists = await page.$(config.RESULTS_TABLE_SELECTOR) !== null;
        if (!resultsTableExists) {
            console.error('❌ Search results table not found. Product might not exist or page structure changed.');
        } else {
            console.warn('⚠️ Prices did not load correctly, attempting to scrape anyway...');
        }
        // Decide whether to continue or bail out
        // For now, let's try to scrape what we can
    }

    await sleep(1000);

    // Check if we can find OEM results
    const oemResultsExist = await page.evaluate((oemTableSelector) => {
      const oemTable = document.querySelector(oemTableSelector);
      return !!oemTable;
    }, config.OEM_RESULTS_TABLE_SELECTOR);

    if (oemResultsExist) {
      console.log('🔍 Found OEM results table');

      // Check for OEM rows
      const oemRowsCount = await page.evaluate((oemTableSelector) => {
        const oemTable = document.querySelector(oemTableSelector);
        if (!oemTable) return 0;
        return oemTable.querySelectorAll('tr[row_type="oe_artikel"]').length;
      }, config.OEM_RESULTS_TABLE_SELECTOR);

      console.log(`🔍 Found ${oemRowsCount} OEM result rows`);
    }

    const scrapedData = await page.evaluate((resultsTableSel, oemTableSel, priceSel, scraperName) => { // Added oemTableSel
      // First, build a map of product codes to delivery information
      const deliveryTermsMap = {};

      // Find all artikel2 rows and extract delivery information
      const artikel2Rows = Array.from(document.querySelectorAll('tr[row_type="artikel2"]'));
      artikel2Rows.forEach(row => {
        // Get the previous row (artikel1)
        const prevRow = row.previousElementSibling;
        if (prevRow && prevRow.getAttribute('row_type') === 'artikel1') {
          // Get the product code from the artikel1 row
          const productCodeEl = prevRow.querySelector('.pnl_link_haendlernr a');
          const productCode = productCodeEl?.textContent.trim();

          if (productCode) {
            // Find the delivery term element in the artikel2 row
            const deliveryTermElement = row.querySelector('.anerp_btn.anerp_deliveryterm');
            if (deliveryTermElement) {
              // Get the inner div text (which contains the delivery time)
              const innerDiv = deliveryTermElement.querySelector('div');
              if (innerDiv) {
                deliveryTermsMap[productCode] = innerDiv.textContent.trim();
              }
            }

            // Find the exchange value element in the artikel2 row
            const exchangeValueElement = row.querySelector('.anerp_btn.anerp_grretur');
            if (exchangeValueElement) {
              // Get the value from the inner div with class anerp_text
              const valueDiv = exchangeValueElement.querySelector('.anerp_text');
              if (valueDiv) {
                const rawText = valueDiv.textContent.trim();
                // Extract the numeric part (e.g., "1.708,00 RON" -> "1.708,00")
                const match = rawText.match(/([\d.,]+)/);
                if (match) {
                  // Handle European number format (1.708,00)
                  // First, remove all dots (thousands separators)
                  // Then, replace comma with dot (decimal separator)
                  const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
                  const numeric = parseFloat(cleanedNumber);
                  if (!isNaN(numeric)) {
                    // Store the exact exchange value in the map without rounding
                    deliveryTermsMap[productCode + '_exchangeValue'] = numeric;
                  }
                }
              }
            }

            // Check if the product has a reduced value option (AVR)
            const avrElement = row.querySelector('.anerp.anerp_avr');
            if (avrElement) {
              // If the AVR element exists, the product can be purchased at a reduced value
              deliveryTermsMap[productCode + '_hasReducedOption'] = true;
            }
          }
        }
      });

      // Now proceed with the main scraping
      const rows = Array.from(document.querySelectorAll(`${resultsTableSel} tr`));
      const products = [];
      let currentName = null;
      let currentBrand = null;

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];

        // Skip header/separator rows if identifiable
        if (row.classList.contains('main_artikel_panel_tr_prio_list_bez')) continue;

        // Identify product name row
        if (row.classList.contains('main_artikel_panel_tr_genart')) {
          const nameSpan = row.querySelector('span');
          currentName = nameSpan ? nameSpan.textContent.trim() : 'N/A';
          currentBrand = null; // Reset brand when a new product type starts
          continue;
        }

        // Identify brand row
        if (row.classList.contains('main_artikel_panel_tr_einspeiser')) {
          const brandSpan = row.querySelector('td[colspan="8"] span'); // Adjust selector if needed
          currentBrand = brandSpan ? brandSpan.textContent.trim() : 'N/A';
          continue;
        }

        // Identify article/item row - now checking for row_type="artikel1"
        if (row.classList.contains('main_artikel_panel_tr_artikel') && row.getAttribute('row_type') === 'artikel1') {
          const productCodeEl = row.querySelector('.pnl_link_haendlernr a');
          const internalCodeEl = row.querySelector('.pnl_link_eartnr a');
          const retailPriceEl = row.querySelector(priceSel); // Use passed selector
          const availabilityEl = row.querySelector('.erp_availIcon_toolTipPanel span'); // Tooltip for delivery
          const deliveryOptionsEl = row.querySelector('.erp_status_pnl span'); // Delivery options

          const productCode = productCodeEl?.textContent.trim() || null;
          const internalCode = internalCodeEl?.textContent.trim() || null;

          // Parse retail price from string to number with 2 decimal places
          let retailPrice = null;
          if (retailPriceEl) {
            const priceText = retailPriceEl.textContent.trim();
            // Extract numeric part (e.g., "2.151,88 RON" -> "2.151,88")
            const match = priceText.match(/([\d.,]+)/);
            if (match) {
              // Handle European number format (2.151,88)
              // First, remove all dots (thousands separators)
              // Then, replace comma with dot (decimal separator)
              const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
              const numeric = parseFloat(cleanedNumber);
              if (!isNaN(numeric)) {
                // Use the exact value without rounding
                retailPrice = numeric;
              }
            }
          }

          const availability = availabilityEl?.textContent.trim() || null;
          const delivery_options = deliveryOptionsEl?.textContent.trim() || null;

          // Get delivery from the map we created
          const delivery = productCode ? deliveryTermsMap[productCode] || null : null;

          // Get exchange value from the map we created
          const exchangeValue = productCode ? deliveryTermsMap[productCode + '_exchangeValue'] || null : null;

          // Get hasReducedOption from the map we created
          const hasReducedOption = productCode ? !!deliveryTermsMap[productCode + '_hasReducedOption'] : false;

          if (productCode) {
            // TODO: Implement availabilitySummary assignment logic
            // Possible values: "Unavailable", "Available", "External Delivery Only"
            let availabilitySummary = "Available"; // Placeholder - will be updated with proper logic

            products.push({
              name: currentName || 'N/A',
              brand: currentBrand || 'N/A',
              "productCode" : internalCode,
              "internalCode" : productCode,
              delivery,
              retailPrice,
              exchangeValue,
              availability,
              availabilitySummary,
              returnable: !(delivery_options == "Disponibil in stocul furnizorului. Detalii in coş." || delivery_options == "Indisponibil"),
              provider: scraperName,
              hasReducedOption,
            });
          }
        }
      }

      // Process OEM results if they exist
      const oemTable = document.querySelector(oemTableSel);
      if (oemTable) {
        // Find all OEM rows
        const oemRows = Array.from(oemTable.querySelectorAll('tr[row_type="oe_artikel"]'));

        for (const oemRow of oemRows) {
          // Extract brand/manufacturer - try to get it from the row directly
          let brand = 'OEM';

          // Try to get brand from the info cell
          const brandEl = oemRow.querySelector('.tc_info div div:first-child span');
          if (brandEl) {
            brand = brandEl.textContent.trim();
          }

          // Extract product codes
          const internalCodeEl = oemRow.querySelector('.tc_number div div:first-child span');
          const productCodeEl = oemRow.querySelector('.tc_number div div:last-child span');

          // For OEM parts, we don't have a specific name field, so we'll use a generic name
          // We could also try to get it from the articledescr attribute if available
          const articleDescr = oemRow.getAttribute('articledescr');

          // Get the OEM article number for better identification
          const oeArticleNo = oemRow.getAttribute('oearticleno');

          // Extract price
          const priceTable = oemRow.querySelector('.tc_price table');
          let retailPrice = null;
          if (priceTable) {
            const priceEl = priceTable.querySelector('.tbl_al_erp_price_spalte_prVal span');
            if (priceEl) {
              const priceText = priceEl.textContent.trim();
              // Extract numeric part (e.g., "332,30 RON*" -> "332,30")
              const match = priceText.match(/([\d.,]+)/);
              if (match) {
                // Handle European number format (332,30)
                const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
                const numeric = parseFloat(cleanedNumber);
                if (!isNaN(numeric)) {
                  retailPrice = numeric;
                }
              }
            }
          }

          // Extract availability/delivery options
          const availabilityEl = oemRow.querySelector('.erp_availIcon_toolTipPanel span');
          const deliveryOptionsEl = oemRow.querySelector('.erp_status_pnl span');

          const internalCode = internalCodeEl?.textContent.trim() || null;
          const productCode = productCodeEl?.textContent.trim() || null;
          // Use articleDescr if available, otherwise use a generic name with OEM article number
          const name = articleDescr || (oeArticleNo ? `OEM Part (${oeArticleNo})` : 'OEM Part');
          const availability = availabilityEl?.textContent.trim() || null;
          const delivery_options = deliveryOptionsEl?.textContent.trim() || null;

          // For OEM parts, we don't have artikel2 rows with delivery/exchange info
          // So we set these values to null or default values
          const delivery = null;
          const exchangeValue = null;
          const hasReducedOption = false;

          if (internalCode && productCode) {
            // TODO: Implement availabilitySummary assignment logic for OEM parts
            // Possible values: "Unavailable", "Available", "External Delivery Only"
            let availabilitySummary = "Unknown"; // Placeholder - will be updated with proper logic

            products.push({
              name,
              brand,
              productCode,
              internalCode,
              delivery,
              retailPrice,
              exchangeValue,
              availability,
              availabilitySummary,
              returnable: !(delivery_options == "Disponibil in stocul furnizorului. Detalii in coş." || delivery_options == "Indisponibil"),
              provider: scraperName,
              hasReducedOption,
              //isOEM: true // Add a flag to indicate this is an OEM part
            });
          }
        }
      }

      return products;
    }, config.RESULTS_TABLE_SELECTOR, config.OEM_RESULTS_TABLE_SELECTOR, config.PRICE_SELECTOR, SCRAPER_NAME); // Added OEM table selector

    await browser.close();
    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully.`);
    return scrapedData;

  } catch (error) {
    console.error(`❌ An error occurred during the ${SCRAPER_NAME} scraping process:`, error);
    if (browser) {
      await browser.close();
    }
    return null; // Indicate failure
  }
}

// Example usage (can be called from src/index.js later)
// runAutonetScraper('GDB1550');

module.exports = {
  runAutonetScraper,
};